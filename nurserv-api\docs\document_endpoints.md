# Document Management Endpoints

This document describes the endpoints for managing user documents stored in AWS S3.

## Upload a Document

`POST /api/documents/upload`

Uploads a document to AWS S3 and stores its metadata in the database.

### Request

- Content-Type: `multipart/form-data`
- Authentication: Required (JWT token)

#### Form Fields

| Field         | Type    | Required | Description                                      |
|---------------|---------|----------|--------------------------------------------------|
| file          | File    | Yes      | The file to upload                               |
| description   | String  | No       | Description of the document                      |
| document_type | String  | No       | Type of document (e.g., "resume", "certificate") |
| is_public     | Boolean | No       | Whether the document is publicly accessible      |

### Response

**Success (201 Created)**

```json
{
  "message": "Document uploaded successfully to AWS S3",
  "document": {
    "id": 1,
    "user_id": "user123",
    "file_name": "user123/abc123.pdf",
    "original_name": "resume.pdf",
    "s3_key": "user123/abc123.pdf",
    "s3_url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf",
    "content_type": "application/pdf",
    "size": 12345,
    "description": "My resume",
    "document_type": "resume",
    "is_public": false,
    "storage_provider": "aws_s3",
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:00:00Z"
  },
  "signed_url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf?AWSAccessKeyId=..."
}
```

**Error (400 Bad Request)**

```json
{
  "error": "No file uploaded"
}
```

**Error (500 Internal Server Error)**

```json
{
  "error": "Failed to upload document: [error message]"
}
```

## List User Documents

`GET /api/documents`

Retrieves a list of documents for the current user.

### Request

- Authentication: Required (JWT token)

#### Query Parameters

| Parameter     | Type    | Required | Description                                      |
|---------------|---------|----------|--------------------------------------------------|
| page          | Integer | No       | Page number (default: 1)                         |
| limit         | Integer | No       | Items per page (default: 10)                     |
| document_type | String  | No       | Filter by document type                          |

### Response

**Success (200 OK)**

```json
{
  "documents": [
    {
      "id": 1,
      "user_id": "user123",
      "file_name": "user123/abc123.pdf",
      "original_name": "resume.pdf",
      "s3_key": "user123/abc123.pdf",
      "s3_url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf",
      "content_type": "application/pdf",
      "size": 12345,
      "description": "My resume",
      "document_type": "resume",
      "is_public": false,
      "storage_provider": "aws_s3",
      "created_at": "2023-06-01T12:00:00Z",
      "updated_at": "2023-06-01T12:00:00Z"
    }
  ],
  "pagination": {
    "total": 5,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

**Error (500 Internal Server Error)**

```json
{
  "error": "Failed to list documents"
}
```

## Get Document by ID

`GET /api/documents/:id`

Retrieves a specific document by its ID.

### Request

- Authentication: Required (JWT token)

### Response

**Success (200 OK)**

```json
{
  "document": {
    "id": 1,
    "user_id": "user123",
    "file_name": "user123/abc123.pdf",
    "original_name": "resume.pdf",
    "s3_key": "user123/abc123.pdf",
    "s3_url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf",
    "content_type": "application/pdf",
    "size": 12345,
    "description": "My resume",
    "document_type": "resume",
    "is_public": false,
    "storage_provider": "aws_s3",
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:00:00Z"
  },
  "signed_url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf?AWSAccessKeyId=..."
}
```

**Error (404 Not Found)**

```json
{
  "error": "Document not found"
}
```

**Error (403 Forbidden)**

```json
{
  "error": "Access denied"
}
```

**Error (500 Internal Server Error)**

```json
{
  "error": "Failed to get document"
}
```

## Update Document

`PUT /api/documents/:id`

Updates a document's metadata.

### Request

- Content-Type: `application/json`
- Authentication: Required (JWT token)

#### Body

```json
{
  "description": "Updated description",
  "document_type": "certificate",
  "is_public": "true"
}
```

### Response

**Success (200 OK)**

```json
{
  "message": "Document updated successfully",
  "document": {
    "id": 1,
    "user_id": "user123",
    "file_name": "user123/abc123.pdf",
    "original_name": "resume.pdf",
    "s3_key": "user123/abc123.pdf",
    "s3_url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf",
    "content_type": "application/pdf",
    "size": 12345,
    "description": "Updated description",
    "document_type": "certificate",
    "is_public": true,
    "storage_provider": "aws_s3",
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:30:00Z"
  }
}
```

**Error (404 Not Found)**

```json
{
  "error": "Document not found"
}
```

**Error (403 Forbidden)**

```json
{
  "error": "Access denied"
}
```

**Error (500 Internal Server Error)**

```json
{
  "error": "Failed to update document"
}
```

## Get Document URL

`GET /api/documents/s3/:s3Key/url`

Generates a signed URL for downloading a document.

### Request

- Authentication: Required (JWT token)

### Response

**Success (200 OK)**

```json
{
  "url": "https://bucket-name.s3.amazonaws.com/user123/abc123.pdf?AWSAccessKeyId=..."
}
```

**Error (500 Internal Server Error)**

```json
{
  "error": "Failed to generate document URL"
}
```

## Delete Document

`DELETE /api/documents/s3/:s3Key`

Deletes a document from AWS S3 and the database.

### Request

- Authentication: Required (JWT token)

### Response

**Success (200 OK)**

```json
{
  "message": "Document deleted successfully"
}
```

**Error (500 Internal Server Error)**

```json
{
  "error": "Failed to delete document"
}
```

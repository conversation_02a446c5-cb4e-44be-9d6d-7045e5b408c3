const { pool } = require('../config/database');

class ProfileImage {
  constructor(data) {
    this.id = data.id;
    this.cognito_id = data.cognito_id;
    this.username = data.username;
    this.email = data.email;
    this.profile_image_name = data.profile_image_name;
    this.s3_key = data.s3_key;
    this.s3_url = data.s3_url;
    this.updated_at = data.updated_at;
  }

  static async updateProfileImage(userId, imageData) {
    try {
      const [result] = await pool.query(
        `UPDATE users
         SET profile_image_name = ?, s3_key = ?, s3_url = ?, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30')
         WHERE cognito_id = ?`,
        [
          imageData.profile_image_name,
          imageData.s3_key,
          imageData.s3_url,
          userId
        ]
      );

      if (result.affectedRows === 0) {
        throw new Error('User not found');
      }

      return this.findByUserId(userId);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update profile image in database');
    }
  }

  static async findByUserId(userId) {
    try {
      const [rows] = await pool.query(
        'SELECT id, cognito_id, username, email, profile_image_name, s3_key, s3_url, updated_at FROM users WHERE cognito_id = ?',
        [userId]
      );
      return rows.length ? new ProfileImage(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user profile image');
    }
  }

  static async findByS3Key(s3Key) {
    try {
      const [rows] = await pool.query(
        'SELECT id, cognito_id, username, email, profile_image_name, s3_key, s3_url, updated_at FROM users WHERE s3_key = ?',
        [s3Key]
      );
      return rows.length ? new ProfileImage(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by S3 key');
    }
  }

  static async deleteProfileImage(userId) {
    try {
      const [result] = await pool.query(
        `UPDATE users
         SET profile_image_name = NULL, s3_key = NULL, s3_url = NULL, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30')
         WHERE cognito_id = ?`,
        [userId]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete profile image from database');
    }
  }

  static async hasProfileImage(userId) {
    try {
      const [rows] = await pool.query(
        'SELECT s3_key FROM users WHERE cognito_id = ? AND s3_key IS NOT NULL',
        [userId]
      );
      return rows.length > 0;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to check profile image existence');
    }
  }
}

module.exports = ProfileImage;

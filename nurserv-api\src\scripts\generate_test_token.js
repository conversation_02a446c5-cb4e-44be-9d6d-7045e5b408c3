const jwt = require('jsonwebtoken');
require('dotenv').config();

// Generate a test token for development purposes
function generateTestToken(userId, email, username) {
  const payload = {
    sub: userId,
    email: email,
    preferred_username: username,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // 24 hours
  };

  const token = jwt.sign(payload, 'test-secret');
  return token;
}

// Generate a test token
const testToken = generateTestToken('test-user-123', '<EMAIL>', 'testuser');
console.log('Test JWT Token:', testToken);

module.exports = { generateTestToken };

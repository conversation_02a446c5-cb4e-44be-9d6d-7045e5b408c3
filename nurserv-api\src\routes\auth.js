const express = require('express');
const { check } = require('express-validator');
const AuthController = require('../controllers/authController');
const checkDatabaseConnection = require('../middleware/database');

const router = express.Router();

// Validation middleware for user registration
const validateRegistration = [
  check('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .isAlphanumeric()
    .withMessage('Username can only contain letters and numbers'),
  check('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters'),
  check('email')
    .isEmail()
    .withMessage('Must be a valid email address'),
  check('phone_number')
    .optional()
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)'),
  check('name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Name cannot exceed 100 characters')
];

// Validation middleware for phone login
const validatePhoneLogin = [
  check('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)'),
  check('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Validation middleware for forgot password
const validateForgotPassword = [
  check('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)')
];

const validateConfirmForgotPassword = [
  check('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)'),
  check('confirmationCode')
    .notEmpty()
    .withMessage('Confirmation code is required'),
  check('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
];

const validateResendOTP = [
  check('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)')
];

const validateChangePassword = [
  check('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)'),
  check('old_password')
    .notEmpty()
    .withMessage('Old password is required'),
  check('new_password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

const validateDeleteAccount = [
  check('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Phone number must be in E.164 format (e.g., +***********)'),
  check('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Public endpoints (no authentication required)
router.post('/register', validateRegistration, checkDatabaseConnection, AuthController.register);
router.post('/confirm', checkDatabaseConnection,AuthController.confirm);
router.post('/login/phone', validatePhoneLogin, checkDatabaseConnection, AuthController.loginWithPhoneNumber);

// Forgot password endpoints
router.post('/forgot-password', validateForgotPassword, checkDatabaseConnection, AuthController.forgotPassword);
router.post('/confirm-forgot-password', validateConfirmForgotPassword, checkDatabaseConnection, AuthController.confirmForgotPassword);
router.post('/resend-otp', validateResendOTP, checkDatabaseConnection, AuthController.resendOTP);
router.post('/change-password', validateChangePassword, checkDatabaseConnection, AuthController.changePassword);
router.post('/delete-account', validateDeleteAccount, checkDatabaseConnection, AuthController.deleteAccount);

module.exports = router;
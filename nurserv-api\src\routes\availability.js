const express = require('express');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const NurseAvailabilityController = require('../controllers/availabilityController');
const checkDatabaseConnection = require('../middleware/database');
const {  body } = require('express-validator');

const router = express.Router();

/// Validation middleware for multiple availabilities
const validateMultipleAvailabilities = [
    body('availability_slots')
    .isArray({ min: 1 })
    .withMessage('availability_slots must be a non-empty array'),
    body('availability_slots.*.availability_slot')
    .notEmpty()
    .withMessage('Each availability slot is required')
    .isISO8601()
    .withMessage('Each availability slot must be in ISO 8601 format'),
    body('availability_slots.*.hourly_fare')
    .isInt({ min: 1 })
    .withMessage('Each hourly fare must be a positive integer')
];

// Middleware to check for specific user types
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// All routes require authentication
router.use(authenticateToken);
router.use(requireUserType);
router.use(checkDatabaseConnection);

// === USER OPERATIONS (authenticated nurse manages their own availability) ===

// Create new availability slot for authenticated user
// router.post('/create', validateAvailability, NurseAvailabilityController.createMyAvailability);

// NEW: Create multiple availability slots at once
router.post('/create',requireNurse, validateMultipleAvailabilities, NurseAvailabilityController.createMultipleAvailabilities);

// Get all my availabilities
router.get('/my-availabilities',requireNurseOrCustomer, NurseAvailabilityController.getMyAvailabilities);

// Update specific availability by ID (must be owned by authenticated user)
router.put('/my-availability/:id',requireNurse, validateMultipleAvailabilities, NurseAvailabilityController.updateMyAvailability);

// Delete specific availability by ID (must be owned by authenticated user)
router.delete('/my-availability/:id',requireNurse, NurseAvailabilityController.deleteMyAvailability);

// Delete ALL my availabilities
router.delete('/my-availabilities',requireNurse, NurseAvailabilityController.deleteAllMyAvailabilities);

// === PUBLIC/ADMIN OPERATIONS (nurseId as parameter) ===

// Get user details for availability form (public access to nurse info)
router.get('/user/:nurseId',requireNurseOrCustomer, NurseAvailabilityController.getUserDetailsForAvailability);

// Get availability for a specific nurse (public view)
router.get('/nurse/:nurseId',requireNurseOrCustomer, NurseAvailabilityController.getNurseAvailability);

// Get all availabilities with filtering (admin/search purposes)
router.get('/',requireNurseOrCustomer, NurseAvailabilityController.getAllAvailabilities);

module.exports = router;
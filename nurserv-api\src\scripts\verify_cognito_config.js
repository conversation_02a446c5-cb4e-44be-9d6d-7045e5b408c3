const AWS = require('aws-sdk');
require('dotenv').config();

async function verifyCognitoConfig() {
  // Configure AWS
  AWS.config.update({
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  });

  const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();

  try {
    // Get User Pool details
    const userPoolParams = {
      UserPoolId: process.env.NURSE_COGNITO_USER_POOL_ID
    };

    console.log('Verifying User Pool configuration...');
    console.log('User Pool ID:', process.env.NURSE_COGNITO_USER_POOL_ID);
    console.log('Region:', process.env.AWS_REGION);

    const userPoolDetails = await cognitoIdentityServiceProvider.describeUserPool(userPoolParams).promise();
    console.log('\nUser Pool Details:', JSON.stringify(userPoolDetails, null, 2));

    // Get App Client details
    const listClientsParams = {
      UserPoolId: process.env.NURSE_COGNITO_USER_POOL_ID,
      MaxResults: 60
    };

    const clients = await cognitoIdentityServiceProvider.listUserPoolClients(listClientsParams).promise();
    console.log('\nApp Clients:', JSON.stringify(clients, null, 2));

    // Verify JWKS endpoint
    const jwksUri = `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.NURSE_COGNITO_USER_POOL_ID}/.well-known/jwks.json`;
    console.log('\nJWKS URI:', jwksUri);

  } catch (error) {
    console.error('Error verifying Cognito configuration:', error);
  }
}

verifyCognitoConfig(); 
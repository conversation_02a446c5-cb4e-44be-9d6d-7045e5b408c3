const express = require('express');
const router = express.Router();
const bankDetailsController = require('../controllers/bankDetailsController');
const { createBankDetailsValidation, updateBankDetailsValidation } = require('../middleware/bankDetailsValidation');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const { decryptRequestData } = require('../middleware/decryptionMiddleware');

router.use(authenticateToken);
router.use(requireUserType);

const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// Create/Update bank details (single account) - decrypt BEFORE validation
router.post('/', requireNurse, decryptRequestData, createBankDetailsValidation, bankDetailsController.createOrUpdateBankDetails);

// Get bank details for the authenticated user
router.get('/', requireNurseOrCustomer, bankDetailsController.getBankDetails);

// Update bank details (no ID needed) - decrypt BEFORE validation  
router.put('/', requireNurse, decryptRequestData, updateBankDetailsValidation, bankDetailsController.updateBankDetails);

// Delete bank details
router.delete('/', requireNurse, bankDetailsController.deleteBankDetails);

module.exports = router;
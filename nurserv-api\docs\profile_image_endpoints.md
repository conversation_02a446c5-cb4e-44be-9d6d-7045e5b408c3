# Profile Image Management Endpoints

This document describes the endpoints for managing user profile images stored in AWS S3.

## Overview

The profile image system allows nurses to upload and manage their profile images, while both nurses and customers can view profile images. Images are stored in AWS S3 and metadata is stored in the `users` table.

## Environment Variables Required

```env
AWS_REGION=your-aws-region
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_PROFILE_BUCKET_NAME=your-profile-images-bucket-name
DB_NAME=test_nurserv_api
```

## Database Schema

The following columns are added to the `users` table:

```sql
ALTER TABLE users
ADD COLUMN profile_image_name VARCHAR(255) NULL,
ADD COLUMN s3_key VARCHAR(255) NULL,
ADD COLUMN s3_url VARCHAR(512) NULL;
```

## Endpoints

### 1. Upload Profile Image

`POST /nurseApi/profile-image/upload`

Uploads a profile image to AWS S3 and stores its metadata in the database.

**Access:** Nurse only

**Request:**
- Content-Type: `multipart/form-data`
- Authentication: Required (JWT token)
- File size limit: 5MB
- Allowed file types: JPEG, PNG, GIF, WebP, BMP, SVG

**Form Fields:**
- `image` (file): The image file to upload

**Response (201 Created):**
```json
{
  "message": "Profile image uploaded successfully to AWS S3",
  "profile_image": {
    "profile_image_name": "profile.jpg",
    "s3_key": "profile-images/user123/abc123.jpg",
    "s3_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg",
    "signed_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg?AWSAccessKeyId=...",
    "updated_at": "2023-06-01T12:00:00Z"
  }
}
```

### 2. Get Current User's Profile Image

`GET /nurseApi/profile-image/me`

Retrieves the authenticated user's profile image.

**Access:** Nurse and Customer

**Response (200 OK):**
```json
{
  "message": "Profile image retrieved successfully",
  "profile_image": {
    "profile_image_name": "profile.jpg",
    "s3_key": "profile-images/user123/abc123.jpg",
    "s3_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg",
    "signed_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg?AWSAccessKeyId=...",
    "updated_at": "2023-06-01T12:00:00Z"
  }
}
```

### 3. Get Profile Image by User ID

`GET /nurseApi/profile-image/user/:userId`

Retrieves a specific user's profile image.

**Access:** Nurse and Customer

**Parameters:**
- `userId` (string): The Cognito ID of the user

**Response (200 OK):**
```json
{
  "message": "Profile image retrieved successfully",
  "profile_image": {
    "profile_image_name": "profile.jpg",
    "s3_key": "profile-images/user123/abc123.jpg",
    "s3_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg",
    "signed_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg?AWSAccessKeyId=...",
    "updated_at": "2023-06-01T12:00:00Z"
  }
}
```

### 4. Get Signed URL for Profile Image

`GET /nurseApi/profile-image/s3/:s3Key/url`

Generates a signed URL for downloading a profile image by S3 key.

**Access:** Nurse and Customer

**Parameters:**
- `s3Key` (string): The S3 key of the profile image

**Response (200 OK):**
```json
{
  "url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/abc123.jpg?AWSAccessKeyId=..."
}
```

### 5. Update Profile Image

`PUT /nurseApi/profile-image/update`

Updates the user's profile image (replaces the existing one).

**Access:** Nurse only

**Request:**
- Content-Type: `multipart/form-data`
- Authentication: Required (JWT token)
- File size limit: 5MB
- Allowed file types: JPEG, PNG, GIF, WebP, BMP, SVG

**Form Fields:**
- `image` (file): The new image file

**Response (200 OK):**
```json
{
  "message": "Profile image updated successfully",
  "profile_image": {
    "profile_image_name": "new_profile.jpg",
    "s3_key": "profile-images/user123/def456.jpg",
    "s3_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/def456.jpg",
    "signed_url": "https://bucket-name.s3.amazonaws.com/profile-images/user123/def456.jpg?AWSAccessKeyId=...",
    "updated_at": "2023-06-01T13:00:00Z"
  }
}
```

### 6. Delete Profile Image

`DELETE /nurseApi/profile-image/delete`

Deletes the user's profile image from both S3 and the database.

**Access:** Nurse only

**Response (200 OK):**
```json
{
  "message": "Profile image deleted successfully"
}
```

## Error Responses

**400 Bad Request:**
```json
{
  "error": "No image file uploaded"
}
```

**403 Forbidden:**
```json
{
  "error": "Access denied: Nurse access required",
  "userType": "customer"
}
```

**404 Not Found:**
```json
{
  "error": "No profile image found for this user"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Failed to upload profile image: File size exceeds 5MB limit"
}
```

## File Validation

- **Size Limit:** 5MB maximum
- **File Types:** JPEG, PNG, GIF, WebP, BMP, SVG
- **Storage:** AWS S3 with private ACL
- **URL Expiration:** Signed URLs expire in 1 hour

## Access Control

- **Upload/Update/Delete:** Nurse only
- **View/Download:** Both Nurse and Customer
- **Authentication:** Required for all endpoints
- **User Type Validation:** Enforced through middleware

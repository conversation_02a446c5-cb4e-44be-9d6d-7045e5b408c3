const ServiceProviderMasterService = require('../services/serviceProviderMasterService');

class ServiceProviderMasterController {
  static async getAllServiceProviders(req, res) {
    try {
      const providers = await ServiceProviderMasterService.getAllServiceProviders();
      res.json(providers);
    } catch (error) {
      console.error('Controller error:', error);
      res.status(500).json({ error: 'Failed to fetch service providers' });
    }
  }

  static async getServiceProviderById(req, res) {
    try {
      const { id } = req.params;
      const provider = await ServiceProviderMasterService.getServiceProviderById(id);
      res.json(provider);
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Service provider not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to fetch service provider' });
      }
    }
  }
}

module.exports = ServiceProviderMasterController; 
const UserService = require('../services/userService');
const AuthService = require('../services/authService');

class UserController {
  static async getProfile(req, res) {
    try {
      const user = await UserService.findByUsername(req.body.username);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        phone_number: user.phone_number,
        created_at: user.created_at,
        updated_at: user.updated_at,
        is_confirmed: user.is_confirmed,
        profile_verified: user.profile_verified,
        // Include location data if available
        latitude: user.latitude,
        longitude: user.longitude,
        address: user.address,
        nurse_onboard_complete: user.nurse_onboard_complete,
        nurse_set_location: user.nurse_set_location
      };

      res.json(userResponse);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      res.status(500).json({ error: 'Failed to fetch user profile' });
    }
  }

  // Get user by ID or username
static async getUser(req, res) {
  try {
    const { idOrUsername } = req.params;
    let user;
    if (isNaN(idOrUsername)) {
      user = await UserService.findByUsername(idOrUsername);
    } else {
      user = await UserService.findById(idOrUsername);
    }
    if (!user) return res.status(404).json({ error: 'User not found' });
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      phone_number: user.phone_number,
      created_at: user.created_at,
      updated_at: user.updated_at,
      is_confirmed: user.is_confirmed,
      profile_verified: user.profile_verified,
      latitude: user.latitude,
      longitude: user.longitude,
      address: user.address,
      nurse_onboard_complete: user.nurse_onboard_complete,
      nurse_set_location: user.nurse_set_location
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user' });
  }
}

  // Admin: verify profile
static async verifyProfile(req, res) {
  try {
    const { idOrUsername } = req.params;
    const updated = await UserService.updateProfileVerified(idOrUsername, true);
    if (!updated) return res.status(404).json({ error: 'User not found' });
    
    res.json({ success: true, message: 'Profile verified.' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to verify profile' });
  }
}

  static async updateProfile(req, res) {
    try {
      const { given_name, family_name, phone_number } = req.body;

      if (phone_number && !phone_number.match(/^\+[1-9]\d{1,14}$/)) {
        return res.status(400).json({ error: 'Phone number must be in E.164 format (e.g., +12345678900)' });
      }

      // Update user in database
      const updatedUser = await UserService.updateProfile(req.user.username, {
        given_name,
        family_name,
        phone_number
      });

      if (!updatedUser) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Update user attributes in Cognito
      const cognitoAttributes = {};
      if (given_name) cognitoAttributes.given_name = given_name;
      if (family_name) cognitoAttributes.family_name = family_name;
      if (phone_number) cognitoAttributes.phone_number = phone_number;

      if (Object.keys(cognitoAttributes).length > 0) {
        await AuthService.updateUserAttributes(req.user.email, cognitoAttributes);
      }

      res.json(updatedUser);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ error: 'Failed to update user profile' });
    }
  }

  static async deleteProfile(req, res) {
    try {
      // Delete user from Cognito
      await AuthService.deleteUser(req.user.email);

      // Delete user from database
      const deleted = await UserService.deleteUser(req.user.username);
      if (!deleted) {
        return res.status(404).json({ error: 'User not found' });
      }

      res.json({ message: 'User deleted successfully' });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({ error: 'Failed to delete user' });
    }
  }

  static async listUsers(req, res) {
    try {
      const result = await UserService.listUsers(req.query);
      res.json(result);
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  }

  static async getUserById(req, res) {
    try {
      const user = await UserService.findById(req.params.userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        phone_number: user.phone_number,
        created_at: user.created_at,
        updated_at: user.updated_at,
        is_confirmed: user.is_confirmed,
          profile_verified: user.profile_verified,
        // Include location data if available
        latitude: user.latitude,
        longitude: user.longitude,
        address: user.address,

      };

      res.json(userResponse);
    } catch (error) {
      console.error('Error fetching user:', error);
      res.status(500).json({ error: 'Failed to fetch user details' });
    }
  }

  static async updateLocation(req, res) {
    try {
      // console.log(" inside update lcoation : ", req)
      const { latitude, longitude, address } = req.body;

      // Validate latitude and longitude if provided
      if (latitude && (isNaN(parseFloat(latitude)) || parseFloat(latitude) < -90 || parseFloat(latitude) > 90)) {
        return res.status(400).json({ error: 'Latitude must be a number between -90 and 90' });
      }

      if (longitude && (isNaN(parseFloat(longitude)) || parseFloat(longitude) < -180 || parseFloat(longitude) > 180)) {
        return res.status(400).json({ error: 'Longitude must be a number between -180 and 180' });
      }

      // console.log(" user sub :: ",req.user)
      // Update user location in database
      const updatedUser = await UserService.updateLocation(req.user.email, {
        latitude: latitude ? parseFloat(latitude) : null,
        longitude: longitude ? parseFloat(longitude) : null,
        address,

      });

      if (!updatedUser) {
        return res.status(404).json({ error: 'User not found' });
      }

      res.json({
        message: 'Location updated successfully',
        user: {
          username: updatedUser.username,
          latitude: updatedUser.latitude,
          longitude: updatedUser.longitude,
          address: updatedUser.address,
          given_name: updatedUser.given_name,
        }
      });
    } catch (error) {
      console.error('Error updating user location:', error);
      res.status(500).json({ error: 'Failed to update user location' });
    }
  }
}

module.exports = UserController;
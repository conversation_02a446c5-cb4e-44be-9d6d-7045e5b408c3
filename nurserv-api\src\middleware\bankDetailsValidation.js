const { body } = require('express-validator');

const createBankDetailsValidation = [
  body('account_holder_name')
    .trim()
    .notEmpty()
    .withMessage('Account holder name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Account holder name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Account holder name should contain only letters and spaces')
    .customSanitizer(value => value.substring(0, 100)), // Truncate to max length

  body('account_no')
    .trim()
    .notEmpty()
    .withMessage('Account number is required')
    .isLength({ min: 9, max: 18 })
    .withMessage('Account number must be between 9 and 18 digits')
    .isNumeric()
    .withMessage('Account number should contain only numbers')
    .customSanitizer(value => value.substring(0, 18)), // Truncate to max length

  body('bank_name')
    .trim()
    .notEmpty()
    .withMessage('Bank name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Bank name must be between 2 and 100 characters')
    .customSanitizer(value => value.substring(0, 100)), // Truncate to max length

  body('branch')
    .trim()
    .notEmpty()
    .withMessage('Branch is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Branch must be between 2 and 100 characters')
    .customSanitizer(value => value.substring(0, 100)), // Truncate to max length

  body('ifsc_code')
    .trim()
    .notEmpty()
    .withMessage('IFSC code is required')
    .isLength({ min: 11, max: 11 })
    .withMessage('IFSC code must be exactly 11 characters')
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/)
    .withMessage('Invalid IFSC code format')
    .customSanitizer(value => value.substring(0, 11)), // Truncate to max length

  body('address')
    .trim()
    .notEmpty()
    .withMessage('Address is required')
    .isLength({ min: 10, max: 500 })
    .withMessage('Address must be between 10 and 500 characters')
    .customSanitizer(value => value.substring(0, 500)), // Truncate to max length

  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('Status must be either active or inactive')
];

const updateBankDetailsValidation = [
  body('account_holder_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Account holder name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Account holder name should contain only letters and spaces')
    .customSanitizer(value => value ? value.substring(0, 100) : value),

  body('account_no')
    .optional()
    .trim()
    .isLength({ min: 9, max: 18 })
    .withMessage('Account number must be between 9 and 18 digits')
    .isNumeric()
    .withMessage('Account number should contain only numbers')
    .customSanitizer(value => value ? value.substring(0, 18) : value),

  body('bank_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Bank name must be between 2 and 100 characters')
    .customSanitizer(value => value ? value.substring(0, 100) : value),

  body('branch')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Branch must be between 2 and 100 characters')
    .customSanitizer(value => value ? value.substring(0, 100) : value),

  body('ifsc_code')
    .optional()
    .trim()
    .isLength({ min: 11, max: 11 })
    .withMessage('IFSC code must be exactly 11 characters')
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/)
    .withMessage('Invalid IFSC code format')
    .customSanitizer(value => value ? value.substring(0, 11) : value),

  body('address')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Address must be between 10 and 500 characters')
    .customSanitizer(value => value ? value.substring(0, 500) : value),

  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('Status must be either active or inactive')
];

module.exports = {
  createBankDetailsValidation,
  updateBankDetailsValidation
};
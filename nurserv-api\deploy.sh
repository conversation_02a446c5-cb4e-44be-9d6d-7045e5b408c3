#!/bin/bash

# Exit on error
set -e

echo "Starting deployment process..."

# Check if .env file exists
if [ ! -f .env ]; then
  echo "Error: .env file not found. Please create one with your environment variables."
  exit 1
fi

# Build the Docker image
echo "Building Docker image..."
docker-compose build

# Stop any running containers
echo "Stopping any running containers..."
docker-compose down

# Start the containers
echo "Starting containers..."
docker-compose up -d

# Check if the application is running
echo "Checking if the application is running..."
sleep 5
if curl -s http://testnurservapi.nurserv.com/health > /dev/null; then
  echo "Application is running successfully!"
else
  echo "Error: Application failed to start. Check the logs with 'docker-compose logs'"
  exit 1
fi

echo "Deployment completed successfully!" 
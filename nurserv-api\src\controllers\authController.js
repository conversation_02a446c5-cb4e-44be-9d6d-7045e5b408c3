const AuthService = require('../services/authService');
const { validationResult } = require('express-validator');

class AuthController {
  static async register(req, res) {
    try {
      const result = await AuthService.register(req.body);
      res.status(201).json(result);
    } catch (error) {
      console.error('Registration error:', error);

      switch (error.code) {
        case 'UsernameExistsException':
          res.status(409).json({ error: 'Username already exists in Cognito' });
          break;
        case 'InvalidPasswordException':
          res.status(400).json({ error: 'Password does not meet Cognito requirements' });
          break;
        case 'InvalidParameterException':
          res.status(400).json({ error: 'Invalid parameter provided to Cognito' });
          break;
        default:
          if (error.message.includes('database')) {
            res.status(500).json({ error: error.message });
          } else {
            res.status(500).json({ error: 'Server error. Please try again later.' });
          }
      }
    }
  }

  static async confirm(req, res) {
    try {
      // console.log('Confirmation request received', req.body);
      const { username, confirmationCode } = req.body;

      if (!username || !confirmationCode) {
        return res.status(400).json({
          error: 'Missing required fields. Username and confirmation code are required.'
        });
      }

      const result = await AuthService.confirmRegistration(username, confirmationCode);
      // console.log('Confirmation successful', result);
      res.json(result);
    } catch (error) {
      console.error('Confirmation error:', error);

      switch (error.code) {
        case 'CodeMismatchException':
          res.status(400).json({ error: 'Invalid confirmation code' });
          break;
        case 'ExpiredCodeException':
          res.status(400).json({ error: 'Confirmation code has expired' });
          break;
        default:
          res.status(500).json({ error: 'Server error. Please try again later.' });
      }
    }
  }

  static async loginWithPhoneNumber(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }
      // console.log('Login request received', req.body);
      const { phone_number, password } = req.body;

      const result = await AuthService.loginWithPhoneNumber(phone_number, password);
      // console.log('Login successful');
      res.json(result);
    } catch (error) {
      console.error('Login error:', error);

      switch (error.code) {
        case 'UserNotFoundException':
          res.status(404).json({ error: 'User not found' });
          break;
        case 'NotAuthorizedException':
          res.status(401).json({ error: 'Incorrect username or password' });
          break;
        case 'UserNotConfirmedException':
          res.status(403).json({ error: 'User is not confirmed' });
          break;
        case 'InvalidParameterException':
          res.status(400).json({ error: error.message || 'Invalid parameters' });
          break;
        default:
          if (error.message === 'User not found') {
            res.status(404).json({ error: 'User not found' });
          } else if (error.message === 'User is not confirmed') {
            res.status(403).json({ error: 'User is not confirmed' });
          } else if (error.message.includes('Authentication failed')) {
            res.status(401).json({ error: error.message });
          } else {
            console.error('Detailed error:', error);
            res.status(500).json({
              error: 'Server error. Please try again later.',
              details: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
          }
      }
    }
  }

  static async forgotPassword(req, res) {
    try {
      const { phone_number } = req.body;

      if (!phone_number) {
        return res.status(400).json({ error: 'Phone number is required' });
      }

      await AuthService.forgotPassword(phone_number);

      res.json({
        message: 'Password reset code has been sent to your phone number'
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'User not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Invalid phone number format') {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to process forgot password request' });
      }
    }
  }

  static async confirmForgotPassword(req, res) {
    try {
      const { phone_number, confirmationCode, newPassword } = req.body;

      if (!phone_number || !confirmationCode || !newPassword) {
        return res.status(400).json({ error: 'Phone number, confirmation code, and new password are required' });
      }

      await AuthService.confirmForgotPassword(phone_number, confirmationCode, newPassword);

      res.json({
        message: 'Password has been reset successfully'
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'User not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Invalid confirmation code') {
        res.status(400).json({ error: error.message });
      } else if (error.message === 'Invalid confirmation code or password format') {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to reset password' });
      }
    }
  }

  static async resendOTP(req, res) {
    try {
      const { phone_number } = req.body;

      if (!phone_number) {
        return res.status(400).json({ error: 'Phone number is required' });
      }

      await AuthService.resendOTP(phone_number);

      res.json({
        message: 'OTP has been resent to your phone number'
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'User not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Invalid phone number format') {
        res.status(400).json({ error: error.message });
      } else if (error.message === 'Too many attempts. Please try again later') {
        res.status(429).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to resend OTP' });
      }
    }
  }

  static async changePassword(req, res) {
    try {
      const { phone_number, old_password, new_password } = req.body;

      if (!phone_number || !old_password || !new_password) {
        return res.status(400).json({ error: 'Phone number, old password, and new password are required' });
      }

      await AuthService.changePassword(phone_number, old_password, new_password);

      res.json({
        message: 'Password has been changed successfully'
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'User not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Invalid old password') {
        res.status(401).json({ error: error.message });
      } else if (error.message === 'New password does not meet requirements') {
        res.status(400).json({ error: error.message });
      } else if (error.message === 'Too many attempts. Please try again later') {
        res.status(429).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to change password' });
      }
    }
  }

  static async deleteAccount(req, res) {
    try {
      const { phone_number, password } = req.body;

      if (!phone_number || !password) {
        return res.status(400).json({ error: 'Phone number and password are required' });
      }

      await AuthService.deleteAccount(phone_number, password);

      res.json({
        message: 'Account has been deleted successfully'
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'User not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Invalid password') {
        res.status(401).json({ error: error.message });
      } else if (error.message === 'Too many attempts. Please try again later') {
        res.status(429).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to delete account' });
      }
    }
  }
}

module.exports = AuthController;
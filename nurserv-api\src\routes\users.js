const express = require('express');
const UserController = require('../controllers/userController');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const checkDatabaseConnection = require('../middleware/database');
//const cacheMiddleware = require('../middleware/cache');

const router = express.Router();

// Middleware to check for specific user types
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// Apply general user type requirement to all routes
router.use(authenticateToken);
router.use(requireUserType);

// Protected endpoints with specific user type requirements

// Nurse-only endpoints
router.post('/profile', checkDatabaseConnection, requireNurse, UserController.getProfile);
router.put('/profile', checkDatabaseConnection, requireNurse, UserController.updateProfile);
router.delete('/profile', checkDatabaseConnection, requireNurse, UserController.deleteProfile);
router.put('/location', checkDatabaseConnection, requireNurse, UserController.updateLocation);

// Nurse and Customer endpoints
router.get('/profile/:idOrUsername', checkDatabaseConnection, requireNurseOrCustomer, UserController.getUser);
router.get('/', checkDatabaseConnection, requireNurseOrCustomer, UserController.listUsers);
router.get('/:userId', checkDatabaseConnection, requireNurseOrCustomer, UserController.getUserById);

module.exports = router;
const TimeSlotsService = require('../services/timeSlotsService');

class TimeSlotsController {
  static async getAllTimeSlots(req, res) {
    try {
      const slots = await TimeSlotsService.getAllTimeSlots();
      res.json({
        success: true,
        data: slots,
        message: 'Time slots retrieved successfully'
      });
    } catch (error) {
      console.error('Controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch time slots',
        message: error.message
      });
    }
  }

  static async getTimeSlotById(req, res) {
    try {
      const { id } = req.params;
      const slot = await TimeSlotsService.getTimeSlotById(id);
      res.json({
        success: true,
        data: slot,
        message: 'Time slot retrieved successfully'
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Time slot not found') {
        res.status(404).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to fetch time slot',
          message: error.message
        });
      }
    }
  }
}

module.exports = TimeSlotsController;
const bankDetailsService = require("../services/bankDetailsService");
const { validationResult } = require("express-validator");

class BankDetailsController {
  async createOrUpdateBankDetails(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const cognitoId = req.user.sub;
      const bankData = req.body;

      if (!bankData.status) {
        bankData.status = "active";
      }

      const result = await bankDetailsService.createOrUpdateBankDetails(
        cognitoId,
        bankData
      );

      res.status(result.isNew ? 201 : 200).json({
        success: true,
        message: result.isNew
          ? "Bank details created successfully"
          : "Bank details updated successfully",
        data: result.data,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  async getBankDetails(req, res) {
    try {
      const cognitoId = req.user.sub;
      console.log("Getting bank details for user:", cognitoId);

      const result = await bankDetailsService.getBankDetailsByUser(cognitoId);

      if (!result) {
        console.log("No valid bank details found, returning null");
        return res.status(200).json({
          success: true,
          message: "No bank details found",
          data: null,
        });
      }

      console.log("Returning bank details for user:", cognitoId);
      res.status(200).json({
        success: true,
        message: "Bank details retrieved successfully",
        data: result,
      });
    } catch (error) {
      console.error("Error getting bank details:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error while retrieving bank details",
        data: null,
      });
    }
  }

  async updateBankDetails(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const cognitoId = req.user.sub;

      // Data is already decrypted by middleware, use directly
      const updateData = req.body;

      const result = await bankDetailsService.updateBankDetails(
        cognitoId,
        updateData
      );

      res.status(200).json({
        success: true,
        message: "Bank details updated successfully",
        data: result,
      });
    } catch (error) {
      const statusCode = error.message === "Bank details not found" ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }

  async deleteBankDetails(req, res) {
    try {
      const cognitoId = req.user.sub;
      const result = await bankDetailsService.deleteBankDetails(cognitoId);

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      const statusCode = error.message === "Bank details not found" ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }
}

module.exports = new BankDetailsController();

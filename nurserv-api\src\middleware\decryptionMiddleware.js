const encryptionUtil = require("../utils/encryption");

const decryptRequestData = (req, res, next) => {
  try {
    // Check if the request body contains encrypted data
    if (req.body && req.body.encrypted && req.body.data) {
      console.log('Decrypting request data...');
      
      try {
        // Decrypt the data
        const decryptedData = encryptionUtil.decrypt(req.body.data);
        
        // Replace the request body with decrypted data
        req.body = decryptedData;
        
        console.log('Successfully decrypted request data');
      } catch (decryptionError) {
        console.error('Failed to decrypt request data:', decryptionError);
        return res.status(400).json({
          success: false,
          message: "Invalid encrypted data format",
        });
      }
    }
    
    // Continue to next middleware
    next();
  } catch (error) {
    console.error('Error in decryption middleware:', error);
    return res.status(500).json({
      success: false,
      message: "Internal server error during decryption",
    });
  }
};

module.exports = {
  decryptRequestData
};
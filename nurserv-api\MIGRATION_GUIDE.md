# Migration Guide: Wasabi to AWS S3

This guide documents the migration from Wasabi Cloud Storage to AWS S3 for document storage.

## Changes Made

### 1. Service Layer
- **Removed**: `src/services/wasabiService.js`
- **Updated**: `src/services/documentService.js` - Now includes `storage_provider: 'aws_s3'` field

### 2. Controller Layer
- **Removed**: `src/controllers/wasabiDocumentController.js`
- **Updated**: `src/controllers/documentController.js` - Fixed missing userId parameter and updated success message

### 3. Routes
- **Removed**: `src/routes/wasabiDocuments.js`
- **Updated**: `src/server.js` - Now uses `/nurseApi/documents` instead of `/nurseApi/wasabi-documents`

### 4. Database Model
- **Updated**: `src/models/document.js` - Added `storage_provider` field support

### 5. Configuration
- **Removed**: `src/config/wasabi.js`
- **Updated**: Uses existing AWS S3 configuration

### 6. Documentation
- **Removed**: `docs/wasabi_document_endpoints.md`
- **Updated**: `docs/document_endpoints.md` - Now reflects AWS S3 integration

## Environment Variables

### Required AWS S3 Variables
```env
AWS_REGION=your-aws-region
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET_NAME=your-s3-bucket-name
```

### Removed Wasabi Variables
The following environment variables are no longer needed:
- `WASABI_ENDPOINT`
- `WASABI_REGION`
- `WASABI_BUCKET_NAME`
- `WASABI_ACCESS_KEY_ID`
- `WASABI_SECRET_ACCESS_KEY`

## Database Migration

Run the following SQL migration to update existing documents:

```sql
-- Update existing documents to use AWS S3 as storage provider
UPDATE documents 
SET storage_provider = 'aws_s3' 
WHERE storage_provider = 'wasabi' OR storage_provider IS NULL;
```

## API Endpoints

The document management endpoints are now available at:
- `POST /nurseApi/documents/upload` - Upload document
- `GET /nurseApi/documents` - List documents
- `GET /nurseApi/documents/:id` - Get document by ID
- `PUT /nurseApi/documents/:id` - Update document
- `GET /nurseApi/documents/s3/:s3Key/url` - Get signed URL
- `DELETE /nurseApi/documents/s3/:s3Key` - Delete document

## Testing

After migration, test the following:
1. Document upload functionality
2. Document retrieval and listing
3. Document URL generation
4. Document updates
5. Document deletion

## Rollback Plan

If rollback is needed:
1. Restore the removed Wasabi files from version control
2. Update `server.js` to use Wasabi routes
3. Update environment variables
4. Run database migration to set storage_provider back to 'wasabi' 
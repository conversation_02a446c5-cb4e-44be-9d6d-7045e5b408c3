const { pool } = require("../config/database");

class BankDetails {
  static async create(bankData) {
    const {
      cognito_id,
      account_holder_name,
      account_no,
      bank_name,
      branch,
      ifsc_code,
      address,
      status = "secondary",
    } = bankData;

    const query = `
      INSERT INTO bank_details 
      (cognito_id, account_holder_name, account_no, bank_name, branch, ifsc_code, address, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, CONVERT_TZ(NOW(), 'UTC', '+05:30'), CONVERT_TZ(NOW(), 'UTC', '+05:30'))
    `;

    try {
      const [result] = await pool.execute(query, [
        cognito_id,
        account_holder_name,
        account_no,
        bank_name,
        branch,
        ifsc_code,
        address,
        status,
      ]);

      return await this.findById(result.insertId);
    } catch (error) {
      if (error.code === "ER_DUP_ENTRY") {
        throw new Error("Bank account already exists for this user");
      }
      throw error;
    }
  }

  static async findById(id) {
    const query = "SELECT * FROM bank_details WHERE id = ?";
    const [rows] = await pool.execute(query, [id]);
    return rows[0] || null;
  }

  static async findByUserAndId(cognitoId, id) {
    const query = "SELECT * FROM bank_details WHERE id = ? AND cognito_id = ?";
    const [rows] = await pool.execute(query, [id, cognitoId]);
    return rows[0] || null;
  }

  static async findByUser(cognitoId, includeSecondary = true) {
    let query = `
    SELECT * FROM bank_details 
    WHERE cognito_id = ?
    AND account_holder_name IS NOT NULL 
    AND account_holder_name != '' 
    AND account_holder_name != 'Unknown'
    AND account_no IS NOT NULL 
    AND account_no != '' 
    AND LENGTH(account_no) >= 9
    AND LENGTH(account_no) <= 18
    AND bank_name IS NOT NULL 
    AND bank_name != '' 
    AND bank_name != 'Unknown Bank'
    AND ifsc_code IS NOT NULL 
    AND ifsc_code != '' 
    AND ifsc_code != 'Unknown IFSC'
    AND LENGTH(ifsc_code) = 11
    AND branch IS NOT NULL 
    AND branch != ''
    AND address IS NOT NULL 
    AND address != ''
  `;

    const params = [cognitoId];

    if (!includeSecondary) {
      query += " AND status = ?";
      params.push("primary");
    }

    query +=
      ' ORDER BY CASE WHEN status = "primary" THEN 0 ELSE 1 END, created_at DESC LIMIT 1';

    try {
      const [rows] = await pool.execute(query, params);
      console.log(
        `Found ${rows.length} valid bank records for user ${cognitoId}`
      );
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error("Error in findByUser:", error);
      throw error;
    }
  }

  static async update(cognitoId, updateData) {
    const fields = [];
    const values = [];

    // Build dynamic update query
    Object.keys(updateData).forEach((key) => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error("No fields to update");
    }

    // Add cognitoId to values array for WHERE clause
    values.push(cognitoId);

    const query = `
    UPDATE bank_details 
    SET ${fields.join(", ")}, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30') 
    WHERE cognito_id = ?
  `;

    const [result] = await pool.execute(query, values);

    if (result.affectedRows === 0) {
      throw new Error("Bank details not found");
    }

    // Return the updated record
    const [rows] = await pool.execute(
      "SELECT * FROM bank_details WHERE cognito_id = ?",
      [cognitoId]
    );

    return rows[0];
  }

  static async delete(cognitoId) {
    const query = "DELETE FROM bank_details WHERE cognito_id = ?";
    const [result] = await pool.execute(query, [cognitoId]);

    if (result.affectedRows === 0) {
      throw new Error("Bank details not found");
    }

    return { message: "Bank details deleted successfully" };
  }
  static async updatePrimaryStatus(cognitoId) {
    const query =
      'UPDATE bank_details SET status = "secondary" WHERE cognito_id = ? AND status = "primary"';
    await pool.execute(query, [cognitoId]);
  }

  static async setPrimary(id, cognitoId) {
    // Start transaction
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // First, set all accounts to secondary
      await connection.execute(
        'UPDATE bank_details SET status = "secondary" WHERE cognito_id = ? AND status = "primary"',
        [cognitoId]
      );

      // Then set the specified account as primary
      const [result] = await connection.execute(
        'UPDATE bank_details SET status = "primary", updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30") WHERE id = ? AND cognito_id = ?',
        [id, cognitoId]
      );

      if (result.affectedRows === 0) {
        throw new Error("Bank details not found");
      }

      await connection.commit();

      // Fetch updated record
      const [rows] = await connection.execute(
        "SELECT * FROM bank_details WHERE id = ? AND cognito_id = ?",
        [id, cognitoId]
      );

      return rows[0];
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  static async checkDuplicateAccount(cognitoId, accountNo, excludeId = null) {
    let query =
      "SELECT id FROM bank_details WHERE cognito_id = ? AND account_no = ?";
    const params = [cognitoId, accountNo];

    if (excludeId) {
      query += " AND id != ?";
      params.push(excludeId);
    }

    const [rows] = await pool.execute(query, params);
    return rows.length > 0;
  }
}

module.exports = BankDetails;

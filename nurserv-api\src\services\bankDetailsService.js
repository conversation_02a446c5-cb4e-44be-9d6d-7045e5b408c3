const BankDetails = require("../models/bankDetails");
const encryptionUtil = require("../utils/encryption");

class BankDetailsService {
  async createOrUpdateBankDetails(cognitoId, bankData) {
  try {
    // Check if user already has bank details
    const existingDetails = await BankDetails.findByUser(cognitoId);
    
    if (existingDetails && existingDetails.length > 0) {
      // Update existing details - store decrypted data in DB
      const updatedDetails = await BankDetails.update(cognitoId, bankData);
      return {
        isNew: false,
        data: this.formatBankDetailsResponse(updatedDetails)
      };
    } else {
      // Create new details - store decrypted data in DB
      const bankDetails = await BankDetails.create({
        cognito_id: cognitoId,
        status: bankData.status || 'active',
        ...bankData
      });
      return {
        isNew: true,
        data: this.formatBankDetailsResponse(bankDetails)
      };
    }
  } catch (error) {
    throw error;
  }
}

  async getBankDetailsByUser(cognitoId) {
  try {
    const bankDetails = await BankDetails.findByUser(cognitoId);
    
    console.log('Raw bank details from DB:', bankDetails);
    
    // Return null if no details found or incomplete
    if (!bankDetails || !this.isCompleteBankDetails(bankDetails)) {
      console.log('No complete bank details found for user:', cognitoId);
      return null;
    }
    
    console.log('Returning encrypted bank details for user:', cognitoId);
    return this.formatBankDetailsResponse(bankDetails);
  } catch (error) {
    console.error('Error in getBankDetailsByUser:', error);
    throw error;
  }
}

isCompleteBankDetails(bankDetails) {
  if (!bankDetails) return false;
  
  const requiredFields = [
    'account_holder_name',
    'account_no', 
    'bank_name',
    'ifsc_code',
    'branch',
    'address'
  ];
  
  for (const field of requiredFields) {
    const value = bankDetails[field];
    if (!value || 
        typeof value !== 'string' || 
        value.trim() === '' ||
        value.toLowerCase() === 'unknown' ||
        value === 'Unknown Bank' ||
        value === 'Unknown IFSC') {
      console.log(`Invalid field ${field}:`, value);
      return false;
    }
  }
  
  // Additional length validations
  if (bankDetails.account_no.length < 9 || bankDetails.account_no.length > 18) {
    console.log('Invalid account number length:', bankDetails.account_no);
    return false;
  }
  
  if (bankDetails.ifsc_code.length !== 11) {
    console.log('Invalid IFSC code length:', bankDetails.ifsc_code);
    return false;
  }
  
  console.log('Bank details validation passed');
  return true;
}
  async updateBankDetails(cognitoId, updateData) {
  try {
    // Store decrypted data in DB, but return encrypted response
    const updatedBankDetails = await BankDetails.update(cognitoId, updateData);
    return this.formatBankDetailsResponse(updatedBankDetails);
  } catch (error) {
    throw error;
  }
}

  async deleteBankDetails(cognitoId) {
    try {
      return await BankDetails.delete(cognitoId);
    } catch (error) {
      throw error;
    }
  }

  formatBankDetailsResponse(bankDetails) {
    // For GET requests, encrypt the response data
    const encryptedData = encryptionUtil.encrypt({
      id: bankDetails.id,
      cognito_id: bankDetails.cognito_id,
      account_holder_name: bankDetails.account_holder_name,
      account_no: bankDetails.account_no,
      bank_name: bankDetails.bank_name,
      branch: bankDetails.branch,
      ifsc_code: bankDetails.ifsc_code,
      address: bankDetails.address,
      status: bankDetails.status,
      created_at: bankDetails.created_at,
      updated_at: bankDetails.updated_at,
    });

    return {
      encrypted: true,
      data: encryptedData,
    };
  }
}

module.exports = new BankDetailsService();

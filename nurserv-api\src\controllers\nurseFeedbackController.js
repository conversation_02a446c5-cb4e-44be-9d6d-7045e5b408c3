const nurseFeedbackService = require('../services/nurseFeedbackService');
const userService = require('../services/userService');

class NurseFeedbackController {
  static async submitFeedback(req, res) {
    try {
      const { rating, name, recipientName, comments, nurseId, feedbackId } = req.body;

      if (!rating) {
        return res.status(400).json({ error: 'Rating is required' });
      }

      if (!name) {
        return res.status(400).json({ error: 'Name is required' });
      }

      if (!recipientName) {
        return res.status(400).json({ error: 'Recipient name is required' });
      }

      const feedback = await nurseFeedbackService.submitFeedback(nurseId, {
        rating,
        name,
        recipientName,
        comments,
        feedbackId
      });

      res.status(201).json({
        message: 'Feedback submitted successfully',
        feedback
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Rating must be between 1 and 5') {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to submit feedback' });
      }
    }
  }

  static async getNurseFeedback(req, res) {
    try {
      const nurseId = req.params.nurseId;
      const feedback = await nurseFeedbackService.getNurseFeedback(nurseId);
      res.json(feedback);
    } catch (error) {
      console.error('Controller error:', error);
      res.status(500).json({ error: 'Failed to fetch nurse feedback' });
    }
  }

  static async getUserDetailsForFeedback(req, res) {
    try {
      const { nurseId } = req.params;
      
      // Get user details
      const user = await userService.findByCognitoId(nurseId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Return user details needed for the form
      res.json({
        name: user.given_name || user.name,
        nurseId: user.cognito_id
      });
    } catch (error) {
      console.error('Controller error:', error);
      res.status(500).json({ error: 'Failed to fetch user details' });
    }
  }
}

module.exports = NurseFeedbackController; 